"""
Data preprocessing module for tokenizing and preparing the TinyStories dataset.
Some functions adapted from https://github.com/karpathy/nanoGPT/blob/master/data/openwebtext/prepare.py
"""

import os
import numpy as np
import tiktoken
from datasets import load_dataset
from tqdm.auto import tqdm


def get_tokenizer():
    """Get the GPT-2 tokenizer."""
    return tiktoken.get_encoding("gpt2")


def process_example(example, tokenizer):
    """
    Process a single example by tokenizing the text.
    
    Args:
        example: Dictionary containing 'text' key
        tokenizer: tiktoken tokenizer
        
    Returns:
        Dictionary with 'ids' and 'len' keys
    """
    ids = tokenizer.encode_ordinary(example['text'])  # encode_ordinary ignores any special tokens
    return {'ids': ids, 'len': len(ids)}


def load_and_tokenize_dataset(dataset_name="roneneldan/TinyStories", num_proc=8):
    """
    Load and tokenize the dataset.
    
    Args:
        dataset_name: Name of the dataset to load
        num_proc: Number of processes for tokenization
        
    Returns:
        Tokenized dataset
    """
    print(f"Loading dataset: {dataset_name}")
    ds = load_dataset(dataset_name)
    
    tokenizer = get_tokenizer()
    
    print("Tokenizing dataset...")
    tokenized = ds.map(
        lambda example: process_example(example, tokenizer),
        remove_columns=['text'],
        desc="tokenizing the splits",
        num_proc=num_proc,
    )
    
    return tokenized


def create_binary_files(tokenized_dataset, total_batches=1024):
    """
    Create binary files from tokenized dataset for efficient loading during training.
    
    Args:
        tokenized_dataset: Tokenized dataset from load_and_tokenize_dataset
        total_batches: Number of batches to process the data in
    """
    print("Creating binary files...")
    
    # Concatenate all the ids in each dataset into one large file we can use for training
    for split, dset in tokenized_dataset.items():
        print(f"Processing {split} split...")
        
        arr_len = np.sum(dset['len'], dtype=np.uint64)
        filename = f'{split}.bin'
        dtype = np.uint16  # (can do since enc.max_token_value == 50256 is < 2**16)
        
        arr = np.memmap(filename, dtype=dtype, mode='w+', shape=(arr_len,))
        
        idx = 0
        for batch_idx in tqdm(range(total_batches), desc=f'writing {filename}'):
            # Batch together samples for faster write
            batch = dset.shard(
                num_shards=total_batches, 
                index=batch_idx, 
                contiguous=True
            ).with_format('numpy')
            
            arr_batch = np.concatenate(batch['ids'])
            
            # Write into mmap
            arr[idx : idx + len(arr_batch)] = arr_batch
            idx += len(arr_batch)
            
        arr.flush()
        print(f"Saved {filename} with {arr_len} tokens")


def prepare_data(force_reprocess=False):
    """
    Main function to prepare data for training.
    
    Args:
        force_reprocess: If True, reprocess even if binary files exist
    """
    if not os.path.exists("train.bin") or force_reprocess:
        print("Binary files not found or force_reprocess=True. Processing dataset...")
        
        # Load and tokenize dataset
        tokenized = load_and_tokenize_dataset()
        
        # Create binary files
        create_binary_files(tokenized)
        
        print("Data preparation complete!")
    else:
        print("Binary files already exist. Skipping data preparation.")
        print("Use force_reprocess=True to reprocess the data.")


if __name__ == "__main__":
    prepare_data()
