"""
Training utilities for the small language model.
"""

import torch
from contextlib import nullcontext
from data_utils import get_batch


def estimate_loss(model, eval_iters, batch_size, block_size, device, device_type, ctx):
    """
    Estimate loss on train and validation sets.
    
    Args:
        model: The model to evaluate
        eval_iters: Number of iterations to average over
        batch_size: Batch size for evaluation
        block_size: Block size for evaluation
        device: Device to run evaluation on
        device_type: Type of device ('cuda' or 'cpu')
        ctx: Context manager for autocast
        
    Returns:
        Dictionary with 'train' and 'val' losses
    """
    out = {}
    model.eval()
    
    with torch.inference_mode():
        for split in ['train', 'val']:
            losses = torch.zeros(eval_iters)
            
            for k in range(eval_iters):
                X, Y = get_batch(split, batch_size, block_size, device, device_type)
                with ctx:
                    _, loss = model(X, Y)
                losses[k] = loss.item()
            
            out[split] = losses.mean()
    
    model.train()
    return out


def setup_training_context(device_type, dtype):
    """
    Setup training context for mixed precision training.
    
    Args:
        device_type: Type of device ('cuda' or 'cpu')
        dtype: Data type for autocast
        
    Returns:
        Context manager for autocast
    """
    if device_type == 'cpu':
        return nullcontext()
    else:
        return torch.amp.autocast(device_type=device_type, dtype=dtype)


def setup_optimizer_and_scheduler(model, config):
    """
    Setup optimizer and learning rate scheduler.
    
    Args:
        model: The model to optimize
        config: Training configuration
        
    Returns:
        Tuple of (optimizer, scheduler)
    """
    from torch.optim.lr_scheduler import LinearLR, SequentialLR, CosineAnnealingLR
    
    # Setup optimizer with weight decay for regularization
    optimizer = torch.optim.AdamW(
        model.parameters(), 
        lr=config.learning_rate, 
        betas=config.betas, 
        weight_decay=config.weight_decay, 
        eps=config.eps
    )
    
    # Setup learning rate schedulers
    scheduler_warmup = LinearLR(optimizer, total_iters=config.warmup_steps)
    scheduler_decay = CosineAnnealingLR(
        optimizer, 
        T_max=config.max_iters - config.warmup_steps, 
        eta_min=config.min_lr
    )
    scheduler = SequentialLR(
        optimizer, 
        schedulers=[scheduler_warmup, scheduler_decay], 
        milestones=[config.warmup_steps]
    )
    
    return optimizer, scheduler


def setup_grad_scaler(dtype):
    """
    Setup gradient scaler for mixed precision training.
    
    Args:
        dtype: Data type string
        
    Returns:
        GradScaler instance
    """
    return torch.amp.GradScaler('cuda', enabled=(dtype == 'float16'))


def save_checkpoint(model, optimizer, scheduler, epoch, loss, filepath):
    """
    Save training checkpoint.
    
    Args:
        model: Model to save
        optimizer: Optimizer state
        scheduler: Scheduler state
        epoch: Current epoch
        loss: Current loss
        filepath: Path to save checkpoint
    """
    checkpoint = {
        'epoch': epoch,
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'loss': loss,
    }
    torch.save(checkpoint, filepath)


def load_checkpoint(model, optimizer, scheduler, filepath, device):
    """
    Load training checkpoint.
    
    Args:
        model: Model to load state into
        optimizer: Optimizer to load state into
        scheduler: Scheduler to load state into
        filepath: Path to checkpoint file
        device: Device to map tensors to
        
    Returns:
        Tuple of (start_epoch, best_loss)
    """
    checkpoint = torch.load(filepath, map_location=device)
    
    model.load_state_dict(checkpoint['model_state_dict'])
    optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
    scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
    
    return checkpoint['epoch'], checkpoint['loss']
