"""
Data loading utilities for training.
Some functions adapted from https://github.com/karpathy/nanoGPT/blob/master/train.py
"""

import torch
import numpy as np


def get_batch(split, batch_size, block_size, device, device_type):
    """
    Get a batch of data for training or validation.
    
    Args:
        split: 'train' or 'val'/'validation'
        batch_size: Number of sequences in the batch
        block_size: Length of each sequence
        device: Device to move tensors to
        device_type: Type of device ('cuda' or 'cpu')
        
    Returns:
        Tuple of (input_tokens, target_tokens)
    """
    # We recreate np.memmap every batch to avoid a memory leak, as per
    # https://stackoverflow.com/questions/45132940/numpy-memmap-memory-usage-want-to-iterate-once/61472122#61472122
    if split == 'train':
        data = np.memmap('train.bin', dtype=np.uint16, mode='r')
    else:
        # Handle both 'val' and 'validation'
        filename = 'validation.bin' if split == 'validation' else 'val.bin'
        try:
            data = np.memmap(filename, dtype=np.uint16, mode='r')
        except FileNotFoundError:
            # Try the other filename
            alt_filename = 'val.bin' if filename == 'validation.bin' else 'validation.bin'
            data = np.memmap(alt_filename, dtype=np.uint16, mode='r')
    
    # Generate random starting positions
    ix = torch.randint(len(data) - block_size, (batch_size,))
    
    # Create input and target sequences
    x = torch.stack([torch.from_numpy((data[i:i+block_size]).astype(np.int64)) for i in ix])
    y = torch.stack([torch.from_numpy((data[i+1:i+1+block_size]).astype(np.int64)) for i in ix])
    
    # Move to device
    if device_type == 'cuda':
        # pin arrays x,y, which allows us to move them to GPU asynchronously (non_blocking=True)
        x, y = x.pin_memory().to(device, non_blocking=True), y.pin_memory().to(device, non_blocking=True)
    else:
        x, y = x.to(device), y.to(device)
    
    return x, y


class DataLoader:
    """
    A simple data loader class that wraps the get_batch function.
    """
    
    def __init__(self, batch_size, block_size, device, device_type):
        """
        Initialize the data loader.
        
        Args:
            batch_size: Number of sequences in each batch
            block_size: Length of each sequence
            device: Device to move tensors to
            device_type: Type of device ('cuda' or 'cpu')
        """
        self.batch_size = batch_size
        self.block_size = block_size
        self.device = device
        self.device_type = device_type
    
    def get_train_batch(self):
        """Get a training batch."""
        return get_batch('train', self.batch_size, self.block_size, self.device, self.device_type)
    
    def get_val_batch(self):
        """Get a validation batch."""
        return get_batch('val', self.batch_size, self.block_size, self.device, self.device_type)
