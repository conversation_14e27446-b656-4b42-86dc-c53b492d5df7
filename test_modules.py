"""
Test script to verify all modules can be imported and basic functionality works.
"""

def test_imports():
    """Test that all modules can be imported successfully."""
    print("Testing module imports...")
    
    try:
        import config
        print("✓ config module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import config: {e}")
        return False
    
    try:
        import data_preprocessing
        print("✓ data_preprocessing module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import data_preprocessing: {e}")
        return False
    
    try:
        import data_utils
        print("✓ data_utils module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import data_utils: {e}")
        return False
    
    try:
        import model
        print("✓ model module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import model: {e}")
        return False
    
    try:
        import train_utils
        print("✓ train_utils module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import train_utils: {e}")
        return False
    
    try:
        import train
        print("✓ train module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import train: {e}")
        return False
    
    try:
        import inference
        print("✓ inference module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import inference: {e}")
        return False
    
    try:
        import visualize
        print("✓ visualize module imported successfully")
    except ImportError as e:
        print(f"✗ Failed to import visualize: {e}")
        return False
    
    return True


def test_config():
    """Test configuration classes."""
    print("\nTesting configuration...")
    
    try:
        from config import get_default_model_config, get_default_training_config
        
        model_config = get_default_model_config()
        training_config = get_default_training_config()
        
        print(f"✓ Model config: {model_config.n_layer} layers, {model_config.n_embd} embedding dim")
        print(f"✓ Training config: lr={training_config.learning_rate}, batch_size={training_config.batch_size}")
        
        return True
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_model():
    """Test model creation."""
    print("\nTesting model creation...")
    
    try:
        from config import get_default_model_config
        from model import GPT
        
        config = get_default_model_config()
        model = GPT(config)
        
        print(f"✓ Model created with {sum(p.numel() for p in model.parameters())} parameters")
        
        # Test forward pass
        import torch
        batch_size, seq_len = 2, 10
        dummy_input = torch.randint(0, config.vocab_size, (batch_size, seq_len))
        
        with torch.no_grad():
            logits, _ = model(dummy_input)
        
        print(f"✓ Forward pass successful: output shape {logits.shape}")
        
        return True
    except Exception as e:
        print(f"✗ Model test failed: {e}")
        return False


def test_tokenizer():
    """Test tokenizer functionality."""
    print("\nTesting tokenizer...")
    
    try:
        from data_preprocessing import get_tokenizer
        
        tokenizer = get_tokenizer()
        test_text = "Hello, world!"
        
        tokens = tokenizer.encode_ordinary(test_text)
        decoded = tokenizer.decode(tokens)
        
        print(f"✓ Tokenizer test: '{test_text}' -> {len(tokens)} tokens -> '{decoded}'")
        
        return True
    except Exception as e:
        print(f"✗ Tokenizer test failed: {e}")
        return False


def main():
    """Run all tests."""
    print("Module Organization Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_model,
        test_tokenizer,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ All tests passed! The code organization is working correctly.")
    else:
        print("✗ Some tests failed. Please check the error messages above.")
    
    return passed == total


if __name__ == "__main__":
    main()
