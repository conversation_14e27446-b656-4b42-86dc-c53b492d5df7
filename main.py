"""
Main entry point for the small language model training pipeline.
"""

import argparse
import sys
import os

def main():
    """Main function to orchestrate the training pipeline."""
    parser = argparse.ArgumentParser(description='Small Language Model Training Pipeline')
    parser.add_argument('--mode', choices=['prepare', 'train', 'inference', 'visualize', 'all'], 
                       default='all', help='Mode to run')
    parser.add_argument('--force-reprocess', action='store_true', 
                       help='Force reprocessing of data even if binary files exist')
    parser.add_argument('--model-path', type=str, default='best_model_params.pt',
                       help='Path to model file for inference')
    parser.add_argument('--interactive', action='store_true',
                       help='Run interactive inference session')
    parser.add_argument('--demo', action='store_true',
                       help='Run demo generation')
    
    args = parser.parse_args()
    
    print("Small Language Model Training Pipeline")
    print("=" * 50)
    
    if args.mode in ['prepare', 'all']:
        print("\n1. Data Preparation")
        print("-" * 20)
        try:
            from data_preprocessing import prepare_data
            prepare_data(force_reprocess=args.force_reprocess)
        except Exception as e:
            print(f"Error in data preparation: {e}")
            if args.mode == 'prepare':
                return
    
    if args.mode in ['train', 'all']:
        print("\n2. Model Training")
        print("-" * 20)
        try:
            # Check if data files exist
            if not os.path.exists('train.bin'):
                print("Training data not found. Running data preparation first...")
                from data_preprocessing import prepare_data
                prepare_data()
            
            from train import train_model
            model, train_losses, val_losses = train_model()
            print("Training completed successfully!")
            
        except Exception as e:
            print(f"Error in training: {e}")
            if args.mode == 'train':
                return
    
    if args.mode in ['visualize', 'all']:
        print("\n3. Training Visualization")
        print("-" * 20)
        try:
            from visualize import load_and_plot_training_results
            load_and_plot_training_results()
        except Exception as e:
            print(f"Error in visualization: {e}")
            if args.mode == 'visualize':
                return
    
    if args.mode in ['inference', 'all'] or args.interactive or args.demo:
        print("\n4. Model Inference")
        print("-" * 20)
        try:
            from inference import demo_generation, interactive_generation
            
            if args.demo or args.mode == 'all':
                demo_generation(args.model_path)
            
            if args.interactive:
                interactive_generation(args.model_path)
                
        except Exception as e:
            print(f"Error in inference: {e}")
            return
    
    print("\nPipeline completed successfully!")


def setup_environment():
    """Setup the environment and check dependencies."""
    print("Checking dependencies...")
    
    required_packages = ['torch', 'numpy', 'tiktoken', 'datasets', 'tqdm', 'matplotlib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"Missing packages: {missing_packages}")
        print("Please install them using: pip install -r requirements.txt")
        return False
    
    print("All dependencies are available!")
    return True


if __name__ == "__main__":
    # Check environment first
    if not setup_environment():
        sys.exit(1)
    
    # Run main pipeline
    main()
