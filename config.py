"""
Configuration classes and training hyperparameters for the small language model.
"""

from dataclasses import dataclass
import torch


@dataclass
class GPTConfig:
    """Configuration class for GPT model architecture."""
    block_size: int
    vocab_size: int
    n_layer: int
    n_head: int
    n_embd: int
    dropout: float = 0.0
    bias: bool = True


@dataclass
class TrainingConfig:
    """Configuration class for training hyperparameters."""
    # Learning parameters
    learning_rate: float = 1e-4  # more stable training
    max_iters: int = 20000  # increase from 25000
    warmup_steps: int = 1000  # smoother initial train
    min_lr: float = 5e-4  # lower rate
    eval_iters: int = 500  # increased from 100
    
    # Batch parameters
    batch_size: int = 32  # changed from 16, better gradient estimate
    block_size: int = 128  # changed from 64, capture longer range dependencies
    gradient_accumulation_steps: int = 32  # reduced from 50
    
    # Device and precision
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    dtype: str = 'bfloat16' if torch.cuda.is_available() and torch.cuda.is_bf16_supported() else 'float16'
    
    # Optimizer parameters
    betas: tuple = (0.9, 0.95)
    weight_decay: float = 0.1
    eps: float = 1e-9
    
    # Model saving
    best_model_params_path: str = "best_model_params.pt"
    
    @property
    def device_type(self):
        """Get device type for autocast."""
        return 'cuda' if 'cuda' in self.device else 'cpu'
    
    @property
    def ptdtype(self):
        """Get PyTorch dtype from string."""
        dtype_map = {
            'float32': torch.float32, 
            'bfloat16': torch.bfloat16, 
            'float16': torch.float16
        }
        return dtype_map[self.dtype]


def get_default_model_config():
    """Get default model configuration."""
    return GPTConfig(
        vocab_size=50257,     # use the tokenizer's vocab size
        block_size=128,       # or whatever context size you're training with
        n_layer=6,
        n_head=6,
        n_embd=384,
        dropout=0.1,
        bias=True
    )


def get_default_training_config():
    """Get default training configuration."""
    return TrainingConfig()
