"""
Main training script for the small language model.
"""

import torch
from tqdm.auto import tqdm

from config import get_default_model_config, get_default_training_config
from model import GPT
from data_utils import get_batch
from train_utils import (
    estimate_loss, 
    setup_training_context, 
    setup_optimizer_and_scheduler, 
    setup_grad_scaler
)


def train_model(model_config=None, training_config=None, resume_from_checkpoint=None):
    """
    Train the GPT model.
    
    Args:
        model_config: Model configuration (uses default if None)
        training_config: Training configuration (uses default if None)
        resume_from_checkpoint: Path to checkpoint to resume from
        
    Returns:
        Tuple of (trained_model, train_losses, val_losses)
    """
    # Use default configs if not provided
    if model_config is None:
        model_config = get_default_model_config()
    if training_config is None:
        training_config = get_default_training_config()
    
    print(f"Training on device: {training_config.device}")
    print(f"Model config: {model_config}")
    print(f"Training config: {training_config}")
    
    # Set random seed for reproducibility
    torch.manual_seed(42)
    torch.set_default_device(training_config.device)
    
    # Initialize model
    model = GPT(model_config)
    model = model.to(training_config.device)
    
    # Setup training components
    optimizer, scheduler = setup_optimizer_and_scheduler(model, training_config)
    scaler = setup_grad_scaler(training_config.dtype)
    ctx = setup_training_context(training_config.device_type, training_config.ptdtype)
    
    # Training state
    best_val_loss = float('inf')
    train_loss_list = []
    validation_loss_list = []
    start_epoch = 0
    
    # Resume from checkpoint if provided
    if resume_from_checkpoint:
        from train_utils import load_checkpoint
        start_epoch, best_val_loss = load_checkpoint(
            model, optimizer, scheduler, resume_from_checkpoint, training_config.device
        )
        print(f"Resumed training from epoch {start_epoch}")
    
    print("Starting training...")
    
    # Training loop
    for epoch in tqdm(range(start_epoch, training_config.max_iters), desc="Training"):
        # Evaluation
        if epoch % training_config.eval_iters == 0 and epoch != 0:
            losses = estimate_loss(
                model, 
                training_config.eval_iters, 
                training_config.batch_size, 
                training_config.block_size, 
                training_config.device, 
                training_config.device_type, 
                ctx
            )
            
            print(f"Epoch {epoch}: train loss {losses['train']:.4f}, val loss {losses['val']:.4f}")
            print(f"Current learning rate: {optimizer.param_groups[0]['lr']:.5f}")
            
            train_loss_list.append(losses['train'])
            validation_loss_list.append(losses['val'])
            
            # Save best model
            if losses['val'] < best_val_loss:
                best_val_loss = losses['val']
                torch.save(model.state_dict(), training_config.best_model_params_path)
                print(f"New best model saved with validation loss: {best_val_loss:.4f}")
        
        # Training step
        X, y = get_batch(
            "train", 
            training_config.batch_size, 
            training_config.block_size, 
            training_config.device, 
            training_config.device_type
        )
        
        with ctx:
            _, loss = model(X, y)
            loss = loss / training_config.gradient_accumulation_steps
            scaler.scale(loss).backward()
        
        # Gradient accumulation and optimization step
        if ((epoch + 1) % training_config.gradient_accumulation_steps == 0) or (epoch + 1 == training_config.max_iters):
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=0.5)
            
            scaler.step(optimizer)
            scaler.update()
            optimizer.zero_grad(set_to_none=True)
        
        scheduler.step()
    
    print("Training completed!")
    print(f"Best validation loss: {best_val_loss:.4f}")
    
    return model, train_loss_list, validation_loss_list


if __name__ == "__main__":
    # Train with default configurations
    model, train_losses, val_losses = train_model()
    
    # Save final training results
    results = {
        'train_losses': train_losses,
        'val_losses': val_losses
    }
    torch.save(results, 'training_results.pt')
    print("Training results saved to training_results.pt")
