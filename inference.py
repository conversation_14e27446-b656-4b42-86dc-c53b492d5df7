"""
Inference module for text generation with the trained model.
"""

import torch
import tiktoken
from config import get_default_model_config
from model import GPT


def load_trained_model(model_path, model_config=None, device=None):
    """
    Load a trained model from checkpoint.
    
    Args:
        model_path: Path to the saved model state dict
        model_config: Model configuration (uses default if None)
        device: Device to load model on (auto-detect if None)
        
    Returns:
        Loaded GPT model
    """
    if device is None:
        device = "cuda" if torch.cuda.is_available() else "cpu"
    
    if model_config is None:
        model_config = get_default_model_config()
    
    # Create model and load weights
    model = GPT(model_config)
    model.load_state_dict(torch.load(model_path, map_location=torch.device(device)))
    model = model.to(device)
    model.eval()
    
    return model


def get_tokenizer():
    """Get the GPT-2 tokenizer."""
    return tiktoken.get_encoding("gpt2")


def generate_text(model, prompt, max_new_tokens=200, temperature=1.0, top_k=None, device=None):
    """
    Generate text given a prompt.
    
    Args:
        model: Trained GPT model
        prompt: Input text prompt
        max_new_tokens: Maximum number of new tokens to generate
        temperature: Sampling temperature (higher = more random)
        top_k: If set, only sample from top k most likely tokens
        device: Device to run inference on
        
    Returns:
        Generated text string
    """
    if device is None:
        device = next(model.parameters()).device
    
    tokenizer = get_tokenizer()
    
    # Encode the prompt
    context = torch.tensor(tokenizer.encode_ordinary(prompt)).unsqueeze(0).to(device)
    
    # Generate tokens
    with torch.no_grad():
        generated = model.generate(
            context, 
            max_new_tokens=max_new_tokens, 
            temperature=temperature, 
            top_k=top_k
        )
    
    # Decode and return the generated text
    return tokenizer.decode(generated.squeeze().tolist())


def interactive_generation(model_path=None, model_config=None):
    """
    Interactive text generation session.
    
    Args:
        model_path: Path to saved model (uses default if None)
        model_config: Model configuration (uses default if None)
    """
    if model_path is None:
        model_path = "best_model_params.pt"
    
    print("Loading model...")
    try:
        model = load_trained_model(model_path, model_config)
        print(f"Model loaded successfully from {model_path}")
    except FileNotFoundError:
        print(f"Model file {model_path} not found. Please train a model first.")
        return
    
    print("\nInteractive Text Generation")
    print("Enter prompts to generate text. Type 'quit' to exit.")
    print("Commands:")
    print("  - 'temp <value>' to change temperature (e.g., 'temp 0.8')")
    print("  - 'tokens <value>' to change max tokens (e.g., 'tokens 100')")
    print("  - 'topk <value>' to change top-k sampling (e.g., 'topk 50')")
    print("-" * 50)
    
    # Default generation parameters
    temperature = 1.0
    max_tokens = 200
    top_k = None
    
    while True:
        try:
            user_input = input("\nPrompt: ").strip()
            
            if user_input.lower() == 'quit':
                break
            
            # Handle commands
            if user_input.startswith('temp '):
                try:
                    temperature = float(user_input.split()[1])
                    print(f"Temperature set to {temperature}")
                    continue
                except (IndexError, ValueError):
                    print("Invalid temperature. Use: temp <float_value>")
                    continue
            
            if user_input.startswith('tokens '):
                try:
                    max_tokens = int(user_input.split()[1])
                    print(f"Max tokens set to {max_tokens}")
                    continue
                except (IndexError, ValueError):
                    print("Invalid token count. Use: tokens <int_value>")
                    continue
            
            if user_input.startswith('topk '):
                try:
                    if user_input.split()[1].lower() == 'none':
                        top_k = None
                    else:
                        top_k = int(user_input.split()[1])
                    print(f"Top-k set to {top_k}")
                    continue
                except (IndexError, ValueError):
                    print("Invalid top-k value. Use: topk <int_value> or topk none")
                    continue
            
            if not user_input:
                print("Please enter a prompt.")
                continue
            
            # Generate text
            print(f"\nGenerating with temp={temperature}, max_tokens={max_tokens}, top_k={top_k}...")
            generated_text = generate_text(
                model, 
                user_input, 
                max_new_tokens=max_tokens, 
                temperature=temperature, 
                top_k=top_k
            )
            
            print(f"\nGenerated text:\n{generated_text}")
            
        except KeyboardInterrupt:
            print("\nExiting...")
            break
        except Exception as e:
            print(f"Error during generation: {e}")


def demo_generation(model_path=None):
    """
    Run a demo generation with a predefined prompt.
    
    Args:
        model_path: Path to saved model (uses default if None)
    """
    if model_path is None:
        model_path = "best_model_params.pt"
    
    print("Loading model for demo...")
    try:
        model = load_trained_model(model_path)
        print(f"Model loaded from {model_path}")
    except FileNotFoundError:
        print(f"Model file {model_path} not found. Please train a model first.")
        return
    
    # Demo prompts
    prompts = [
        "Once upon a time there was a pumpkin.",
        "The little girl walked into the forest and",
        "In a magical kingdom far away,",
        "The brave knight decided to"
    ]
    
    print("\nDemo Text Generation:")
    print("=" * 50)
    
    for i, prompt in enumerate(prompts, 1):
        print(f"\nDemo {i}: '{prompt}'")
        print("-" * 30)
        
        generated = generate_text(model, prompt, max_new_tokens=100, temperature=0.8)
        print(generated)


if __name__ == "__main__":
    # Run demo generation
    demo_generation()
    
    # Start interactive session
    print("\n" + "="*50)
    interactive_generation()
