# Small Language Model Training Pipeline

This project implements a complete pipeline for training a small GPT-style language model on the TinyStories dataset. The code has been properly organized into modular components for better maintainability and reusability.

## Project Structure

```
slm/
├── README.md                 # This file
├── requirements.txt          # Python dependencies
├── main.py                  # Main entry point and pipeline orchestration
├── app.py                   # Legacy entry point (redirects to main.py)
├── config.py                # Configuration classes and hyperparameters
├── data_preprocessing.py    # Dataset loading and tokenization
├── data_utils.py           # Data loading utilities for training
├── model.py                # GPT model architecture
├── train_utils.py          # Training utilities and helper functions
├── train.py                # Main training script
├── inference.py            # Text generation and inference
└── visualize.py            # Training metrics visualization
```

## Features

- **Modular Architecture**: Clean separation of concerns with dedicated modules
- **Configurable**: Easy-to-modify configuration classes for model and training parameters
- **Data Pipeline**: Efficient data preprocessing and loading with memory mapping
- **Modern Training**: Mixed precision training, gradient accumulation, learning rate scheduling
- **Visualization**: Training loss plots and analysis
- **Interactive Inference**: Command-line interface for text generation
- **Checkpointing**: Model saving and loading capabilities

## Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Complete Pipeline

```bash
python main.py
```

This will:
1. Download and preprocess the TinyStories dataset
2. Train the model
3. Generate visualizations
4. Run demo text generation

### 3. Run Individual Components

#### Data Preparation Only
```bash
python main.py --mode prepare
```

#### Training Only
```bash
python main.py --mode train
```

#### Inference Only
```bash
python main.py --mode inference --interactive
```

#### Visualization Only
```bash
python main.py --mode visualize
```

## Configuration

### Model Configuration
Edit `config.py` to modify model architecture:
- `vocab_size`: Vocabulary size (default: 50257)
- `block_size`: Context length (default: 128)
- `n_layer`: Number of transformer layers (default: 6)
- `n_head`: Number of attention heads (default: 6)
- `n_embd`: Embedding dimension (default: 384)

### Training Configuration
Edit `config.py` to modify training parameters:
- `learning_rate`: Initial learning rate (default: 1e-4)
- `max_iters`: Maximum training iterations (default: 20000)
- `batch_size`: Batch size (default: 32)
- `gradient_accumulation_steps`: Gradient accumulation (default: 32)

## Usage Examples

### Custom Training
```python
from config import GPTConfig, TrainingConfig
from train import train_model

# Custom model config
model_config = GPTConfig(
    vocab_size=50257,
    block_size=256,  # Longer context
    n_layer=8,       # Deeper model
    n_head=8,
    n_embd=512,
    dropout=0.1
)

# Custom training config
training_config = TrainingConfig(
    learning_rate=5e-5,
    max_iters=50000,
    batch_size=16
)

# Train model
model, train_losses, val_losses = train_model(model_config, training_config)
```

### Text Generation
```python
from inference import load_trained_model, generate_text

# Load model
model = load_trained_model("best_model_params.pt")

# Generate text
text = generate_text(
    model, 
    "Once upon a time", 
    max_new_tokens=100, 
    temperature=0.8
)
print(text)
```

### Interactive Generation
```python
from inference import interactive_generation

# Start interactive session
interactive_generation("best_model_params.pt")
```

## Model Architecture

The model implements a GPT-style transformer with:
- Causal self-attention with optional Flash Attention
- Layer normalization
- MLP with GELU activation
- Positional and token embeddings
- Weight tying between embedding and output layers

## Training Features

- **Mixed Precision**: Automatic mixed precision with bfloat16/float16
- **Gradient Accumulation**: Effective larger batch sizes
- **Learning Rate Scheduling**: Linear warmup + cosine annealing
- **Gradient Clipping**: Prevents gradient explosion
- **Checkpointing**: Automatic saving of best models
- **Evaluation**: Regular validation loss monitoring

## File Descriptions

- **`config.py`**: Configuration dataclasses for model and training parameters
- **`data_preprocessing.py`**: Downloads TinyStories dataset, tokenizes with tiktoken, creates binary files
- **`data_utils.py`**: Efficient data loading with memory mapping for training
- **`model.py`**: Complete GPT implementation with attention, MLP, and generation
- **`train_utils.py`**: Training utilities like loss estimation, optimizer setup
- **`train.py`**: Main training loop with mixed precision and checkpointing
- **`inference.py`**: Model loading and text generation with interactive interface
- **`visualize.py`**: Training loss visualization and analysis
- **`main.py`**: Pipeline orchestration with command-line interface

## Requirements

- Python 3.8+
- PyTorch 2.0+
- tiktoken
- datasets (HuggingFace)
- matplotlib
- tqdm
- numpy

## License

This project is based on techniques from nanoGPT by Andrej Karpathy and implements a clean, modular version for educational and research purposes.
