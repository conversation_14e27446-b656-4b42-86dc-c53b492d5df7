"""
Visualization utilities for training metrics.
"""

import matplotlib.pyplot as plt
import torch
import numpy as np


def plot_training_losses(train_losses, val_losses, save_path=None, show=True):
    """
    Plot training and validation losses.
    
    Args:
        train_losses: List of training losses
        val_losses: List of validation losses
        save_path: Path to save the plot (optional)
        show: Whether to display the plot
    """
    # Convert tensors to numpy if needed
    if isinstance(train_losses[0], torch.Tensor):
        train_losses = [loss.cpu().detach().numpy() for loss in train_losses]
    if isinstance(val_losses[0], torch.Tensor):
        val_losses = [loss.cpu().detach().numpy() for loss in val_losses]
    
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, 'g-', label='Training Loss', linewidth=2)
    plt.plot(val_losses, 'r-', label='Validation Loss', linewidth=2)
    plt.xlabel('Evaluation Steps')
    plt.ylabel('Loss')
    plt.title('Training and Validation Loss Over Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()


def plot_learning_rate_schedule(optimizer_states, save_path=None, show=True):
    """
    Plot learning rate schedule over training.
    
    Args:
        optimizer_states: List of optimizer states or learning rates
        save_path: Path to save the plot (optional)
        show: Whether to display the plot
    """
    if isinstance(optimizer_states[0], dict):
        # Extract learning rates from optimizer states
        learning_rates = [state['param_groups'][0]['lr'] for state in optimizer_states]
    else:
        # Assume it's already a list of learning rates
        learning_rates = optimizer_states
    
    plt.figure(figsize=(10, 6))
    plt.plot(learning_rates, 'b-', linewidth=2)
    plt.xlabel('Training Steps')
    plt.ylabel('Learning Rate')
    plt.title('Learning Rate Schedule')
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Learning rate plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()


def plot_loss_comparison(loss_dict, save_path=None, show=True):
    """
    Plot comparison of multiple loss curves.
    
    Args:
        loss_dict: Dictionary with keys as labels and values as loss lists
        save_path: Path to save the plot (optional)
        show: Whether to display the plot
    """
    plt.figure(figsize=(12, 8))
    
    colors = ['blue', 'red', 'green', 'orange', 'purple', 'brown']
    
    for i, (label, losses) in enumerate(loss_dict.items()):
        # Convert tensors to numpy if needed
        if isinstance(losses[0], torch.Tensor):
            losses = [loss.cpu().detach().numpy() for loss in losses]
        
        color = colors[i % len(colors)]
        plt.plot(losses, color=color, label=label, linewidth=2)
    
    plt.xlabel('Evaluation Steps')
    plt.ylabel('Loss')
    plt.title('Loss Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved to {save_path}")
    
    if show:
        plt.show()
    else:
        plt.close()


def load_and_plot_training_results(results_path='training_results.pt', save_plots=True):
    """
    Load training results and create visualizations.
    
    Args:
        results_path: Path to the training results file
        save_plots: Whether to save plots to files
    """
    try:
        results = torch.load(results_path, map_location='cpu')
        train_losses = results['train_losses']
        val_losses = results['val_losses']
        
        print(f"Loaded training results from {results_path}")
        print(f"Training steps: {len(train_losses)}")
        print(f"Final training loss: {train_losses[-1]:.4f}")
        print(f"Final validation loss: {val_losses[-1]:.4f}")
        print(f"Best validation loss: {min(val_losses):.4f}")
        
        # Plot training curves
        save_path = 'training_losses.png' if save_plots else None
        plot_training_losses(train_losses, val_losses, save_path=save_path)
        
        # Plot loss statistics
        plt.figure(figsize=(12, 4))
        
        plt.subplot(1, 2, 1)
        plt.hist(train_losses, bins=20, alpha=0.7, color='green', label='Training')
        plt.hist(val_losses, bins=20, alpha=0.7, color='red', label='Validation')
        plt.xlabel('Loss Value')
        plt.ylabel('Frequency')
        plt.title('Loss Distribution')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        smoothed_train = np.convolve(train_losses, np.ones(5)/5, mode='valid')
        smoothed_val = np.convolve(val_losses, np.ones(5)/5, mode='valid')
        plt.plot(smoothed_train, 'g-', label='Smoothed Training', linewidth=2)
        plt.plot(smoothed_val, 'r-', label='Smoothed Validation', linewidth=2)
        plt.xlabel('Evaluation Steps')
        plt.ylabel('Smoothed Loss')
        plt.title('Smoothed Loss Curves')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plots:
            plt.savefig('loss_analysis.png', dpi=300, bbox_inches='tight')
            print("Loss analysis saved to loss_analysis.png")
        
        plt.show()
        
    except FileNotFoundError:
        print(f"Training results file {results_path} not found.")
        print("Please run training first to generate results.")
    except Exception as e:
        print(f"Error loading training results: {e}")


if __name__ == "__main__":
    # Load and visualize training results
    load_and_plot_training_results()
